# 🤖 تشغيل الوكلاء الذكيين - AI Development Assistant
# =====================================================

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("memory", "file-search", "terminal", "data-analysis", "coordinator", "test", "all")]
    [string]$Agent = "test",
    
    [Parameter(Mandatory=$false)]
    [string]$Task = "",
    
    [Parameter(Mandatory=$false)]
    [switch]$Interactive = $false
)

Write-Host "🤖 AI Agents - AI Development Assistant" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Gray

# التحقق من Ollama
Write-Host "🔍 فحص Ollama..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:11434/api/tags" -Method GET -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        $models = ($response.Content | ConvertFrom-Json).models
        Write-Host "✅ Ollama متصل - النماذج المتاحة: $($models.Count)" -ForegroundColor Green
        foreach ($model in $models) {
            Write-Host "   - $($model.name)" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "❌ Ollama غير متاح! تأكد من تشغيله على المنفذ 11434" -ForegroundColor Red
    Write-Host "💡 لتشغيل Ollama: ollama serve" -ForegroundColor Yellow
    exit 1
}

# اختبار سريع للتوليد
Write-Host "`n🧪 اختبار توليد النص..." -ForegroundColor Yellow
try {
    $testData = @{
        model = "phi3:mini"
        prompt = "مرحبا"
        stream = $false
    } | ConvertTo-Json

    $testResponse = Invoke-WebRequest -Uri "http://localhost:11434/api/generate" -Method POST -Body $testData -ContentType "application/json" -TimeoutSec 10
    if ($testResponse.StatusCode -eq 200) {
        $result = ($testResponse.Content | ConvertFrom-Json).response
        Write-Host "✅ التوليد يعمل: $($result.Substring(0, [Math]::Min(30, $result.Length)))..." -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ تحذير: مشكلة في التوليد - $($_.Exception.Message)" -ForegroundColor Yellow
}

# تشغيل الوكيل المطلوب
Write-Host "`n🚀 تشغيل الوكيل: $Agent" -ForegroundColor Cyan

switch ($Agent) {
    "memory" {
        Write-Host "🧠 تشغيل وكيل الذاكرة..." -ForegroundColor Magenta
        if ($Task) {
            Write-Host "المهمة: $Task" -ForegroundColor Gray
        }
        python memory-agent.py
    }
    
    "file-search" {
        Write-Host "🔍 تشغيل وكيل البحث في الملفات..." -ForegroundColor Blue
        if ($Task) {
            Write-Host "المهمة: $Task" -ForegroundColor Gray
        }
        python file-search-agent.py
    }
    
    "terminal" {
        Write-Host "💻 تشغيل وكيل الترمينال..." -ForegroundColor Green
        if ($Task) {
            Write-Host "المهمة: $Task" -ForegroundColor Gray
        }
        python terminal-agent.py
    }
    
    "data-analysis" {
        Write-Host "📊 تشغيل وكيل تحليل البيانات..." -ForegroundColor Yellow
        if ($Task) {
            Write-Host "المهمة: $Task" -ForegroundColor Gray
        }
        python data-analysis-agent.py
    }
    
    "coordinator" {
        Write-Host "🤖 تشغيل منسق الوكلاء..." -ForegroundColor Cyan
        if ($Task) {
            Write-Host "المهمة: $Task" -ForegroundColor Gray
        }
        python agent-coordinator.py
    }
    
    "test" {
        Write-Host "🧪 تشغيل اختبارات الوكلاء..." -ForegroundColor White
        
        # اختبار بسيط للوكلاء
        Write-Host "`n📋 اختبار الوكلاء:" -ForegroundColor Cyan
        
        # اختبار وكيل الذاكرة
        Write-Host "🧠 اختبار Memory Agent..." -ForegroundColor Magenta
        try {
            $memoryTest = python -c "
from memory_agent import MemoryAgent
agent = MemoryAgent()
report = agent.generate_session_report()
print(f'Sessions: {report.get(\"total_sessions\", 0)}')
print('Memory Agent: OK')
"
            Write-Host "✅ $memoryTest" -ForegroundColor Green
        } catch {
            Write-Host "❌ Memory Agent فشل" -ForegroundColor Red
        }
        
        # اختبار وكيل البحث
        Write-Host "🔍 اختبار File Search Agent..." -ForegroundColor Blue
        try {
            $fileTest = python -c "
from file_search_agent import FileSearchAgent
agent = FileSearchAgent()
files = agent.scan_directory('.', max_files=3)
print(f'Files found: {len(files)}')
print('File Search Agent: OK')
"
            Write-Host "✅ $fileTest" -ForegroundColor Green
        } catch {
            Write-Host "❌ File Search Agent فشل" -ForegroundColor Red
        }
        
        # اختبار وكيل الترمينال
        Write-Host "💻 اختبار Terminal Agent..." -ForegroundColor Green
        try {
            $terminalTest = python -c "
from terminal_agent import TerminalAgent
agent = TerminalAgent()
info = agent.get_system_info()
print(f'OS: {info.get(\"os\", {}).get(\"system\", \"Unknown\")}')
print('Terminal Agent: OK')
"
            Write-Host "✅ $terminalTest" -ForegroundColor Green
        } catch {
            Write-Host "❌ Terminal Agent فشل" -ForegroundColor Red
        }
    }
    
    "all" {
        Write-Host "🌟 تشغيل جميع الوكلاء..." -ForegroundColor White
        
        Write-Host "`n1️⃣ Memory Agent:" -ForegroundColor Magenta
        python memory-agent.py
        
        Write-Host "`n2️⃣ File Search Agent:" -ForegroundColor Blue
        python file-search-agent.py
        
        Write-Host "`n3️⃣ Terminal Agent:" -ForegroundColor Green
        python terminal-agent.py
        
        Write-Host "`n4️⃣ Data Analysis Agent:" -ForegroundColor Yellow
        python data-analysis-agent.py
        
        Write-Host "`n5️⃣ Agent Coordinator:" -ForegroundColor Cyan
        python agent-coordinator.py
    }
    
    default {
        Write-Host "❌ وكيل غير معروف: $Agent" -ForegroundColor Red
        Write-Host "الوكلاء المتاحين: memory, file-search, terminal, data-analysis, coordinator, test, all" -ForegroundColor Yellow
    }
}

if ($Interactive) {
    Write-Host "`n🔄 الوضع التفاعلي - اختر وكيل:" -ForegroundColor Cyan
    Write-Host "1. Memory Agent (🧠)" -ForegroundColor Magenta
    Write-Host "2. File Search Agent (🔍)" -ForegroundColor Blue
    Write-Host "3. Terminal Agent (💻)" -ForegroundColor Green
    Write-Host "4. Data Analysis Agent (📊)" -ForegroundColor Yellow
    Write-Host "5. Agent Coordinator (🤖)" -ForegroundColor Cyan
    Write-Host "0. خروج" -ForegroundColor Red
    
    do {
        $choice = Read-Host "`nاختر رقم الوكيل"
        switch ($choice) {
            "1" { python memory-agent.py }
            "2" { python file-search-agent.py }
            "3" { python terminal-agent.py }
            "4" { python data-analysis-agent.py }
            "5" { python agent-coordinator.py }
            "0" { Write-Host "👋 وداعاً!" -ForegroundColor Green; break }
            default { Write-Host "❌ اختيار غير صحيح" -ForegroundColor Red }
        }
    } while ($choice -ne "0")
}

Write-Host "`n✨ انتهى تشغيل الوكلاء" -ForegroundColor Green
