#!/usr/bin/env python3
"""
Integration API - واجهة التكامل بين الأدوات
نظام موحد للتواصل بين جميع خدمات AI Development Assistant
"""

from flask import Flask, request, jsonify, render_template_string
import requests
import redis
import json
import time
from datetime import datetime
import logging
import os

# إعداد التطبيق
app = Flask(__name__)
app.config['JSON_AS_ASCII'] = False

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# إعداد Redis للذاكرة المشتركة
try:
    redis_client = redis.Redis(
        host=os.getenv('REDIS_HOST', 'shared-memory'),
        port=int(os.getenv('REDIS_PORT', 6379)),
        db=0,
        decode_responses=True
    )
    redis_client.ping()
    logger.info("✅ Redis connected successfully")
except Exception as e:
    logger.error(f"❌ Redis connection failed: {str(e)}")
    redis_client = None

# خريطة الخدمات
SERVICES = {
    'anythingllm': {
        'url': os.getenv('ANYTHINGLLM_URL', 'http://anythingllm:3001'),
        'health_endpoint': '/api/ping',
        'description': 'نظام إدارة المعرفة'
    },
    'n8n': {
        'url': os.getenv('N8N_URL', 'http://n8n:5678'),
        'health_endpoint': '/healthz',
        'description': 'أتمتة العمليات'
    },
    'ai_coordinator': {
        'url': os.getenv('AI_COORDINATOR_URL', 'http://ai-coordinator:3333'),
        'health_endpoint': '/health',
        'description': 'تنسيق الذكاء الاصطناعي'
    },
    'ollama': {
        'url': os.getenv('OLLAMA_URL', 'http://host.docker.internal:11434'),
        'health_endpoint': '/api/tags',
        'description': 'نماذج الذكاء الاصطناعي المحلية'
    },
    'ollama_webui': {
        'url': os.getenv('OLLAMA_WEBUI_URL', 'http://ollama-webui:8080'),
        'health_endpoint': '/health',
        'description': 'واجهة Ollama'
    }
}

class SharedMemory:
    """إدارة الذاكرة المشتركة"""
    
    @staticmethod
    def store_interaction(source, target, data, ttl=3600):
        """حفظ تفاعل بين الخدمات"""
        if not redis_client:
            return False
        
        try:
            interaction = {
                'timestamp': datetime.now().isoformat(),
                'source': source,
                'target': target,
                'data': data
            }
            
            key = f"interaction:{source}:{target}:{int(time.time())}"
            redis_client.setex(key, ttl, json.dumps(interaction))
            return True
        except Exception as e:
            logger.error(f"Failed to store interaction: {str(e)}")
            return False
    
    @staticmethod
    def get_recent_interactions(service=None, limit=10):
        """الحصول على آخر التفاعلات"""
        if not redis_client:
            return []
        
        try:
            pattern = f"interaction:*{service}*" if service else "interaction:*"
            keys = redis_client.keys(pattern)
            
            interactions = []
            for key in sorted(keys)[-limit:]:
                data = redis_client.get(key)
                if data:
                    interactions.append(json.loads(data))
            
            return interactions
        except Exception as e:
            logger.error(f"Failed to get interactions: {str(e)}")
            return []
    
    @staticmethod
    def share_knowledge(service, knowledge_type, content, ttl=86400):
        """مشاركة المعرفة بين الخدمات"""
        if not redis_client:
            return False
        
        try:
            key = f"knowledge:{service}:{knowledge_type}"
            knowledge = {
                'timestamp': datetime.now().isoformat(),
                'service': service,
                'type': knowledge_type,
                'content': content
            }
            redis_client.setex(key, ttl, json.dumps(knowledge))
            return True
        except Exception as e:
            logger.error(f"Failed to share knowledge: {str(e)}")
            return False

def check_service_health(service_name):
    """فحص صحة خدمة"""
    if service_name not in SERVICES:
        return False, "Service not found"
    
    service = SERVICES[service_name]
    try:
        response = requests.get(
            f"{service['url']}{service['health_endpoint']}", 
            timeout=5
        )
        return response.status_code == 200, response.status_code
    except Exception as e:
        return False, str(e)

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    html = """
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Integration API - واجهة التكامل</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
            .header { text-align: center; color: #333; margin-bottom: 30px; }
            .services { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
            .service { background: #f9f9f9; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; }
            .status { padding: 5px 10px; border-radius: 5px; color: white; font-weight: bold; }
            .healthy { background: #28a745; }
            .unhealthy { background: #dc3545; }
            .api-section { margin-top: 30px; padding: 20px; background: #e9ecef; border-radius: 8px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔗 Integration API</h1>
                <p>واجهة التكامل الموحدة لنظام AI Development Assistant</p>
            </div>
            
            <div class="services" id="services">
                <p>جاري تحميل حالة الخدمات...</p>
            </div>
            
            <div class="api-section">
                <h3>📡 API Endpoints المتاحة</h3>
                <ul>
                    <li><strong>GET /api/status</strong> - حالة جميع الخدمات</li>
                    <li><strong>POST /api/coordinate</strong> - تنسيق بين الخدمات</li>
                    <li><strong>GET /api/interactions</strong> - آخر التفاعلات</li>
                    <li><strong>POST /api/knowledge</strong> - مشاركة المعرفة</li>
                    <li><strong>GET /health</strong> - صحة Integration API</li>
                </ul>
            </div>
        </div>
        
        <script>
            // تحديث حالة الخدمات
            function updateServices() {
                fetch('/api/status')
                    .then(response => response.json())
                    .then(data => {
                        const servicesDiv = document.getElementById('services');
                        let html = '';
                        
                        for (const [name, info] of Object.entries(data.services)) {
                            const statusClass = info.healthy ? 'healthy' : 'unhealthy';
                            const statusText = info.healthy ? 'يعمل' : 'متوقف';
                            
                            html += `
                                <div class="service">
                                    <h4>${name}</h4>
                                    <p>${info.description}</p>
                                    <p><strong>URL:</strong> ${info.url}</p>
                                    <span class="status ${statusClass}">${statusText}</span>
                                </div>
                            `;
                        }
                        
                        servicesDiv.innerHTML = html;
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        document.getElementById('services').innerHTML = '<p>خطأ في تحميل البيانات</p>';
                    });
            }
            
            // تحديث كل 30 ثانية
            updateServices();
            setInterval(updateServices, 30000);
        </script>
    </body>
    </html>
    """
    return html

@app.route('/health')
def health():
    """فحص صحة Integration API"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "integration-api",
        "redis_connected": redis_client is not None
    })

@app.route('/api/status')
def get_services_status():
    """حالة جميع الخدمات"""
    status = {
        "timestamp": datetime.now().isoformat(),
        "integration_api": "healthy",
        "redis_connected": redis_client is not None,
        "services": {}
    }
    
    for service_name, service_info in SERVICES.items():
        healthy, details = check_service_health(service_name)
        status["services"][service_name] = {
            "healthy": healthy,
            "details": details,
            "url": service_info["url"],
            "description": service_info["description"]
        }
    
    return jsonify(status)

@app.route('/api/coordinate', methods=['POST'])
def coordinate_services():
    """تنسيق بين الخدمات"""
    try:
        data = request.json
        task = data.get('task')
        source = data.get('source', 'integration-api')
        
        # تسجيل التفاعل
        SharedMemory.store_interaction(source, 'coordination', {
            'task': task,
            'data': data
        })
        
        if task == 'knowledge_query':
            # استعلام من AnythingLLM
            target_url = f"{SERVICES['anythingllm']['url']}/api/v1/workspace/chat"
            response = requests.post(target_url, json=data, timeout=30)
            
            SharedMemory.store_interaction('integration-api', 'anythingllm', {
                'request': data,
                'response': response.json() if response.status_code == 200 else None
            })
            
            return jsonify(response.json()) if response.status_code == 200 else jsonify({"error": "AnythingLLM request failed"})
        
        elif task == 'workflow_trigger':
            # تشغيل workflow في n8n
            target_url = f"{SERVICES['n8n']['url']}/webhook/trigger"
            response = requests.post(target_url, json=data, timeout=30)
            
            SharedMemory.store_interaction('integration-api', 'n8n', {
                'request': data,
                'response': response.text
            })
            
            return jsonify({"status": "triggered", "response": response.text})
        
        elif task == 'ai_generation':
            # توليد من Ollama
            target_url = f"{SERVICES['ollama']['url']}/api/generate"
            response = requests.post(target_url, json=data, timeout=60)
            
            SharedMemory.store_interaction('integration-api', 'ollama', {
                'request': data,
                'response': response.json() if response.status_code == 200 else None
            })
            
            return jsonify(response.json()) if response.status_code == 200 else jsonify({"error": "Ollama request failed"})
        
        else:
            return jsonify({"error": "Unknown task", "available_tasks": ["knowledge_query", "workflow_trigger", "ai_generation"]})
    
    except Exception as e:
        logger.error(f"Coordination error: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/interactions')
def get_interactions():
    """الحصول على آخر التفاعلات"""
    service = request.args.get('service')
    limit = int(request.args.get('limit', 20))
    
    interactions = SharedMemory.get_recent_interactions(service, limit)
    
    return jsonify({
        "interactions": interactions,
        "count": len(interactions),
        "timestamp": datetime.now().isoformat()
    })

@app.route('/api/knowledge', methods=['POST'])
def share_knowledge():
    """مشاركة المعرفة بين الخدمات"""
    try:
        data = request.json
        service = data.get('service')
        knowledge_type = data.get('type')
        content = data.get('content')
        
        if not all([service, knowledge_type, content]):
            return jsonify({"error": "Missing required fields: service, type, content"}), 400
        
        success = SharedMemory.share_knowledge(service, knowledge_type, content)
        
        if success:
            return jsonify({"status": "shared", "timestamp": datetime.now().isoformat()})
        else:
            return jsonify({"error": "Failed to share knowledge"}), 500
    
    except Exception as e:
        logger.error(f"Knowledge sharing error: {str(e)}")
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=False)
