# 🧪 اختبار إعداد AnythingLLM
# ================================

Write-Host "🚀 اختبار إعداد AnythingLLM مع Ollama..." -ForegroundColor Green

# اختبار 1: فحص حالة AnythingLLM
Write-Host "`n📊 فحص حالة AnythingLLM..." -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "http://localhost:4001/api/v1/system/health" -Method GET -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ AnythingLLM: يعمل بنجاح" -ForegroundColor Green
        $healthData = $response.Content | ConvertFrom-Json
        Write-Host "📝 معلومات النظام:" -ForegroundColor Cyan
        Write-Host "   - الحالة: $($healthData.status)" -ForegroundColor White
    }
}
catch {
    Write-Host "❌ AnythingLLM: لا يعمل - $($_.Exception.Message)" -ForegroundColor Red
}

# اختبار 2: فحص حالة Ollama
Write-Host "`n🦙 فحص حالة Ollama..." -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "http://localhost:11434/api/tags" -Method GET -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Ollama: يعمل بنجاح" -ForegroundColor Green
        $models = ($response.Content | ConvertFrom-Json).models
        Write-Host "📋 النماذج المتاحة:" -ForegroundColor Cyan
        foreach ($model in $models) {
            Write-Host "   - $($model.name)" -ForegroundColor White
        }
    }
}
catch {
    Write-Host "❌ Ollama: لا يعمل - $($_.Exception.Message)" -ForegroundColor Red
}

# اختبار 3: اختبار الاتصال بين AnythingLLM و Ollama
Write-Host "`n🔗 اختبار الاتصال بين AnythingLLM و Ollama..." -ForegroundColor Yellow

try {
    # محاولة إرسال طلب بسيط عبر AnythingLLM API
    $testData = @{
        message = "مرحبا، هذا اختبار للاتصال"
        mode = "chat"
    } | ConvertTo-Json

    Write-Host "📤 إرسال رسالة اختبار..." -ForegroundColor Cyan
    # ملاحظة: قد نحتاج لإعداد workspace أولاً
    Write-Host "💡 تذكير: تحتاج لإعداد workspace في AnythingLLM أولاً" -ForegroundColor Yellow
}
catch {
    Write-Host "⚠️ اختبار API: $($_.Exception.Message)" -ForegroundColor Yellow
}

# اختبار 4: فحص Docker containers
Write-Host "`n🐳 فحص حالة Docker containers..." -ForegroundColor Yellow

try {
    $containers = docker ps --filter "name=anythingllm" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    Write-Host "📊 حالة AnythingLLM container:" -ForegroundColor Cyan
    Write-Host $containers -ForegroundColor White
}
catch {
    Write-Host "❌ خطأ في فحص Docker containers" -ForegroundColor Red
}

# اختبار 5: فحص logs
Write-Host "`n📋 فحص logs الأخيرة..." -ForegroundColor Yellow

try {
    Write-Host "📝 آخر 5 سطور من logs AnythingLLM:" -ForegroundColor Cyan
    $logs = docker logs anythingllm --tail 5 2>&1
    Write-Host $logs -ForegroundColor White
}
catch {
    Write-Host "❌ خطأ في قراءة logs" -ForegroundColor Red
}

Write-Host "`n🎯 الخطوات التالية:" -ForegroundColor Green
Write-Host "1. افتح http://localhost:4001 في المتصفح" -ForegroundColor White
Write-Host "2. أكمل الإعداد الأولي لـ AnythingLLM" -ForegroundColor White
Write-Host "3. اختر Ollama كـ LLM Provider" -ForegroundColor White
Write-Host "4. استخدم العنوان: http://host.docker.internal:11434" -ForegroundColor White
Write-Host "5. اختر النموذج: llama3:8b" -ForegroundColor White

Write-Host "`n🎉 انتهى اختبار إعداد AnythingLLM!" -ForegroundColor Green
