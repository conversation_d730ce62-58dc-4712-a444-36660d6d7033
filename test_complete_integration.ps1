# 🎯 اختبار التكامل الكامل للنظام
# =====================================

Write-Host "🚀 بدء اختبار التكامل الكامل للنظام..." -ForegroundColor Green

# المرحلة 1: فحص البنية التحتية
Write-Host "`n🏗️ المرحلة 1: فحص البنية التحتية..." -ForegroundColor Yellow

$infrastructure = @(
    @{Name="AnythingLLM"; URL="http://localhost:4001/api/v1/system/health"; Port=4001},
    @{Name="n8n"; URL="http://localhost:4002/healthz"; Port=4002},
    @{Name="AI Coordinator"; URL="http://localhost:4003/api/health"; Port=4003},
    @{Name="Ollama WebUI"; URL="http://localhost:4000"; Port=4000},
    @{Name="Ollama API"; URL="http://localhost:11434/api/tags"; Port=11434}
)

$infrastructureScore = 0
foreach ($service in $infrastructure) {
    try {
        $connection = Test-NetConnection -ComputerName "localhost" -Port $service.Port -WarningAction SilentlyContinue
        if ($connection.TcpTestSucceeded) {
            Write-Host "✅ $($service.Name): متاح" -ForegroundColor Green
            $infrastructureScore++
        } else {
            Write-Host "❌ $($service.Name): غير متاح" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ $($service.Name): خطأ في الاختبار" -ForegroundColor Red
    }
}

Write-Host "📊 نتيجة البنية التحتية: $infrastructureScore/5" -ForegroundColor Cyan

# المرحلة 2: اختبار النماذج المحلية
Write-Host "`n🤖 المرحلة 2: اختبار النماذج المحلية..." -ForegroundColor Yellow

try {
    $models = ollama list 2>$null
    if ($models) {
        Write-Host "✅ Ollama: يعمل ولديه نماذج" -ForegroundColor Green
        Write-Host "📋 النماذج المتاحة:" -ForegroundColor Cyan
        ollama list
        $modelsScore = 1
    } else {
        Write-Host "❌ Ollama: لا توجد نماذج" -ForegroundColor Red
        $modelsScore = 0
    }
}
catch {
    Write-Host "❌ Ollama: غير متاح" -ForegroundColor Red
    $modelsScore = 0
}

# المرحلة 3: اختبار AI Coordinator
Write-Host "`n🧠 المرحلة 3: اختبار AI Coordinator..." -ForegroundColor Yellow

try {
    $coordinatorTest = @{
        prompt = "مرحبا، اختبار AI Coordinator"
        options = @{
            priority = "fast"
            complexity = "low"
        }
    } | ConvertTo-Json

    $response = Invoke-RestMethod -Uri "http://localhost:4003/api/coordinate" -Method POST -ContentType "application/json" -Body $coordinatorTest -TimeoutSec 15
    Write-Host "✅ AI Coordinator: يعمل بنجاح" -ForegroundColor Green
    Write-Host "📝 الرد: $($response.response)" -ForegroundColor Cyan
    $coordinatorScore = 1
}
catch {
    Write-Host "❌ AI Coordinator: $($_.Exception.Message)" -ForegroundColor Red
    $coordinatorScore = 0
}

# المرحلة 4: اختبار VS Code Integration
Write-Host "`n💻 المرحلة 4: اختبار VS Code Integration..." -ForegroundColor Yellow

try {
    $vscodeTest = node vscode_ai_helper.js health 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ VS Code AI Helper: يعمل" -ForegroundColor Green
        $vscodeScore = 1
    } else {
        Write-Host "⚠️ VS Code AI Helper: يحتاج تحسين" -ForegroundColor Yellow
        $vscodeScore = 0.5
    }
}
catch {
    Write-Host "❌ VS Code AI Helper: خطأ" -ForegroundColor Red
    $vscodeScore = 0
}

# المرحلة 5: اختبار السيناريو الكامل
Write-Host "`n🎭 المرحلة 5: اختبار السيناريو الكامل..." -ForegroundColor Yellow

# سيناريو 1: طلب بسيط عبر VS Code AI Helper
Write-Host "📤 سيناريو 1: طلب بسيط عبر VS Code..." -ForegroundColor Cyan

try {
    $simpleRequest = node vscode_ai_helper.js generate "دالة بسيطة لحساب مجموع رقمين" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ السيناريو البسيط: نجح" -ForegroundColor Green
        $scenario1Score = 1
    } else {
        Write-Host "⚠️ السيناريو البسيط: نجح جزئياً" -ForegroundColor Yellow
        $scenario1Score = 0.5
    }
}
catch {
    Write-Host "❌ السيناريو البسيط: فشل" -ForegroundColor Red
    $scenario1Score = 0
}

# سيناريو 2: طلب معقد عبر n8n (إذا كان متاحاً)
Write-Host "`n📤 سيناريو 2: طلب معقد عبر n8n..." -ForegroundColor Cyan

try {
    $complexRequest = @{
        prompt = "اشرح مفهوم الذكاء الاصطناعي وأعط مثال برمجي"
        complexity = "high"
    } | ConvertTo-Json

    $n8nResponse = Invoke-RestMethod -Uri "http://localhost:4002/webhook/ai-dev-assistant" -Method POST -ContentType "application/json" -Body $complexRequest -TimeoutSec 30
    Write-Host "✅ السيناريو المعقد: نجح" -ForegroundColor Green
    Write-Host "📝 النتيجة متاحة في n8n executions" -ForegroundColor Cyan
    $scenario2Score = 1
}
catch {
    Write-Host "⚠️ السيناريو المعقد: يحتاج إعداد n8n workflow" -ForegroundColor Yellow
    $scenario2Score = 0
}

# المرحلة 6: تقييم الذاكرة والسياق
Write-Host "`n🧠 المرحلة 6: اختبار الذاكرة والسياق..." -ForegroundColor Yellow

try {
    # محاولة الوصول لـ AnythingLLM
    $memoryTest = Invoke-WebRequest -Uri "http://localhost:4001" -Method GET -TimeoutSec 10
    if ($memoryTest.StatusCode -eq 200) {
        Write-Host "✅ AnythingLLM: متاح للذاكرة والسياق" -ForegroundColor Green
        Write-Host "💡 يمكنك إنشاء workspace وإضافة مستندات" -ForegroundColor Cyan
        $memoryScore = 1
    }
}
catch {
    Write-Host "❌ AnythingLLM: غير متاح للذاكرة" -ForegroundColor Red
    $memoryScore = 0
}

# حساب النتيجة الإجمالية
$totalScore = $infrastructureScore + $modelsScore + $coordinatorScore + $vscodeScore + $scenario1Score + $scenario2Score + $memoryScore
$maxScore = 10
$percentage = [math]::Round(($totalScore / $maxScore) * 100, 1)

# النتيجة النهائية
Write-Host "`n🎯 النتيجة النهائية للتكامل:" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host "🏗️ البنية التحتية: $infrastructureScore/5" -ForegroundColor White
Write-Host "🤖 النماذج المحلية: $modelsScore/1" -ForegroundColor White
Write-Host "🧠 AI Coordinator: $coordinatorScore/1" -ForegroundColor White
Write-Host "💻 VS Code Integration: $vscodeScore/1" -ForegroundColor White
Write-Host "🎭 السيناريو البسيط: $scenario1Score/1" -ForegroundColor White
Write-Host "📊 السيناريو المعقد: $scenario2Score/1" -ForegroundColor White
Write-Host "🧠 الذاكرة والسياق: $memoryScore/1" -ForegroundColor White
Write-Host "================================" -ForegroundColor Green
Write-Host "📈 النتيجة الإجمالية: $totalScore/$maxScore ($percentage%)" -ForegroundColor Cyan

# تقييم الحالة
if ($percentage -ge 80) {
    Write-Host "🎉 ممتاز! النظام جاهز للاستخدام الكامل" -ForegroundColor Green
} elseif ($percentage -ge 60) {
    Write-Host "👍 جيد! النظام يعمل مع بعض التحسينات المطلوبة" -ForegroundColor Yellow
} else {
    Write-Host "⚠️ يحتاج عمل! النظام يحتاج إعداد إضافي" -ForegroundColor Red
}

# التوصيات
Write-Host "`n📋 التوصيات للخطوات التالية:" -ForegroundColor Green

if ($infrastructureScore -lt 5) {
    Write-Host "🔧 شغل جميع الخدمات: docker-compose up -d" -ForegroundColor White
}

if ($scenario2Score -eq 0) {
    Write-Host "📥 استورد n8n workflow: ai_integrated_workflow.json" -ForegroundColor White
    Write-Host "🔐 أعد Gemini API credentials في n8n" -ForegroundColor White
}

if ($memoryScore -eq 0) {
    Write-Host "🧠 أعد AnythingLLM وأنشئ workspace" -ForegroundColor White
}

Write-Host "`n🌐 الروابط المهمة:" -ForegroundColor Green
Write-Host "- AnythingLLM: http://localhost:4001" -ForegroundColor White
Write-Host "- n8n: http://localhost:4002 (admin/password123)" -ForegroundColor White
Write-Host "- AI Coordinator: http://localhost:4003" -ForegroundColor White
Write-Host "- Ollama WebUI: http://localhost:4000" -ForegroundColor White

Write-Host "`n🎉 انتهى اختبار التكامل الكامل!" -ForegroundColor Green
