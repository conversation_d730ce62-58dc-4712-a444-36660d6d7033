# 🔄 مساعد النهج المختلط
# =======================

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("quick", "deep", "status", "switch", "help")]
    [string]$Mode,
    
    [Parameter(Mandatory=$false)]
    [string]$Task = ""
)

Write-Host "🔄 مساعد النهج المختلط" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green

switch ($Mode) {
    "quick" {
        Write-Host "`n⚡ الوضع السريع - VS Code" -ForegroundColor Yellow
        Write-Host "للمهام البرمجية السريعة والبسيطة" -ForegroundColor Cyan
        
        if ($Task -eq "") {
            Write-Host "`nالمهام المتاحة:" -ForegroundColor White
            Write-Host "1. analyze <file> - تحليل ملف" -ForegroundColor Gray
            Write-Host "2. generate <description> - توليد كود" -ForegroundColor Gray
            Write-Host "3. health - فحص النظام" -ForegroundColor Gray
            Write-Host "`nمثال: .\hybrid_helper.ps1 quick -Task 'generate دالة حساب المتوسط'" -ForegroundColor Yellow
        } else {
            Write-Host "`n🚀 تنفيذ المهمة السريعة..." -ForegroundColor Cyan
            
            if ($Task.StartsWith("analyze")) {
                $file = $Task.Replace("analyze ", "")
                node vscode_ai_helper.js analyze $file
            }
            elseif ($Task.StartsWith("generate")) {
                $description = $Task.Replace("generate ", "")
                node vscode_ai_helper.js generate $description
            }
            elseif ($Task -eq "health") {
                node vscode_ai_helper.js health
            }
            else {
                Write-Host "❌ مهمة غير معروفة. استخدم: analyze, generate, أو health" -ForegroundColor Red
            }
        }
    }
    
    "deep" {
        Write-Host "`n🧠 الوضع العميق - AnythingLLM" -ForegroundColor Yellow
        Write-Host "للمهام المعقدة والتحليل الاستراتيجي" -ForegroundColor Cyan
        
        Write-Host "`n🌐 فتح AnythingLLM في المتصفح..." -ForegroundColor Green
        Start-Process "http://localhost:4001"
        
        Write-Host "`n💡 استخدم AnythingLLM للمهام التالية:" -ForegroundColor White
        Write-Host "- التخطيط الاستراتيجي للمشاريع" -ForegroundColor Gray
        Write-Host "- البحث في المستندات" -ForegroundColor Gray
        Write-Host "- المحادثات الطويلة والمعقدة" -ForegroundColor Gray
        Write-Host "- بناء قاعدة المعرفة" -ForegroundColor Gray
        Write-Host "- التوثيق الشامل" -ForegroundColor Gray
    }
    
    "status" {
        Write-Host "`n📊 فحص حالة النظام المختلط..." -ForegroundColor Yellow
        
        # فحص الخدمات الأساسية
        $services = @(
            @{Name="AnythingLLM"; URL="http://localhost:4001"; Type="Deep"},
            @{Name="AI Coordinator"; URL="http://localhost:4003"; Type="Quick"},
            @{Name="Ollama"; URL="http://localhost:11434"; Type="Both"},
            @{Name="n8n"; URL="http://localhost:4002"; Type="Advanced"}
        )
        
        Write-Host "`n🔍 حالة الخدمات:" -ForegroundColor Cyan
        foreach ($service in $services) {
            try {
                $response = Invoke-WebRequest -Uri $service.URL -Method GET -TimeoutSec 5
                if ($response.StatusCode -eq 200) {
                    Write-Host "✅ $($service.Name) ($($service.Type)): يعمل" -ForegroundColor Green
                }
            }
            catch {
                Write-Host "❌ $($service.Name) ($($service.Type)): لا يعمل" -ForegroundColor Red
            }
        }
        
        # فحص النماذج
        Write-Host "`n🤖 النماذج المتاحة:" -ForegroundColor Cyan
        try {
            $models = ollama list 2>$null
            if ($models) {
                ollama list
            } else {
                Write-Host "❌ لا توجد نماذج متاحة" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "❌ Ollama غير متاح" -ForegroundColor Red
        }
        
        # توصيات الاستخدام
        Write-Host "`n💡 توصيات الاستخدام:" -ForegroundColor Green
        Write-Host "⚡ للمهام السريعة: .\hybrid_helper.ps1 quick" -ForegroundColor White
        Write-Host "🧠 للمهام المعقدة: .\hybrid_helper.ps1 deep" -ForegroundColor White
        Write-Host "🔄 للتبديل: .\hybrid_helper.ps1 switch" -ForegroundColor White
    }
    
    "switch" {
        Write-Host "`n🔄 مساعد التبديل بين الأدوات" -ForegroundColor Yellow
        
        Write-Host "`nاختر الأداة المناسبة لمهمتك:" -ForegroundColor Cyan
        Write-Host "1. ⚡ VS Code - للبرمجة السريعة" -ForegroundColor White
        Write-Host "2. 🧠 AnythingLLM - للتحليل العميق" -ForegroundColor White
        Write-Host "3. ⚙️ n8n - للأتمتة المتقدمة" -ForegroundColor White
        Write-Host "4. 📊 فحص الحالة" -ForegroundColor White
        
        $choice = Read-Host "`nأدخل رقم اختيارك (1-4)"
        
        switch ($choice) {
            "1" {
                Write-Host "`n⚡ تشغيل VS Code Tasks..." -ForegroundColor Green
                Write-Host "استخدم Ctrl+Shift+P ثم Tasks: Run Task" -ForegroundColor Yellow
            }
            "2" {
                Write-Host "`n🧠 فتح AnythingLLM..." -ForegroundColor Green
                Start-Process "http://localhost:4001"
            }
            "3" {
                Write-Host "`n⚙️ فتح n8n..." -ForegroundColor Green
                Start-Process "http://localhost:4002"
                Write-Host "المستخدم: admin، كلمة المرور: password123" -ForegroundColor Yellow
            }
            "4" {
                & $MyInvocation.MyCommand.Path -Mode "status"
            }
            default {
                Write-Host "❌ اختيار غير صحيح" -ForegroundColor Red
            }
        }
    }
    
    "help" {
        Write-Host "`n📖 دليل الاستخدام:" -ForegroundColor Yellow
        Write-Host "=================" -ForegroundColor Yellow
        
        Write-Host "`n🔄 الأوامر المتاحة:" -ForegroundColor Cyan
        Write-Host "quick - الوضع السريع (VS Code)" -ForegroundColor White
        Write-Host "deep - الوضع العميق (AnythingLLM)" -ForegroundColor White
        Write-Host "status - فحص حالة النظام" -ForegroundColor White
        Write-Host "switch - مساعد التبديل" -ForegroundColor White
        Write-Host "help - عرض هذه المساعدة" -ForegroundColor White
        
        Write-Host "`n💡 أمثلة:" -ForegroundColor Cyan
        Write-Host ".\hybrid_helper.ps1 quick -Task 'generate دالة حساب المتوسط'" -ForegroundColor Gray
        Write-Host ".\hybrid_helper.ps1 quick -Task 'analyze myfile.js'" -ForegroundColor Gray
        Write-Host ".\hybrid_helper.ps1 deep" -ForegroundColor Gray
        Write-Host ".\hybrid_helper.ps1 status" -ForegroundColor Gray
        Write-Host ".\hybrid_helper.ps1 switch" -ForegroundColor Gray
        
        Write-Host "`n🎯 فلسفة النهج المختلط:" -ForegroundColor Green
        Write-Host "⚡ VS Code: للمهام السريعة (< 5 دقائق)" -ForegroundColor White
        Write-Host "🧠 AnythingLLM: للمهام المعقدة (> 5 دقائق)" -ForegroundColor White
        Write-Host "🔄 التبديل: حسب طبيعة المهمة" -ForegroundColor White
        
        Write-Host "`n📚 للمزيد من المعلومات:" -ForegroundColor Green
        Write-Host "code HYBRID_APPROACH_GUIDE.md" -ForegroundColor Gray
    }
}

Write-Host "`n🎉 انتهى تنفيذ المساعد!" -ForegroundColor Green
