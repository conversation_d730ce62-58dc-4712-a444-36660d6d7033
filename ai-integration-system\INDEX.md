# 🤖 AI Integration System - فهرس النظام

## 📁 هيكل المجلدات

```
ai-integration-system/
├── 📋 INDEX.md                    # هذا الملف - فهرس النظام
├── 🚀 quick-start.ps1             # سكريبت البدء السريع
├── 
├── 📂 config/                     # ملفات التكوين
│   ├── ai-integration-system.json # التكوين الرئيسي
│   └── .env.integration          # متغيرات البيئة
│
├── 📂 scripts/                    # السكريبتات التنفيذية
│   ├── ai_integration_controller.py  # منسق Python
│   └── ai-integration-manager.ps1    # مدير PowerShell
│
├── 📂 docs/                       # الوثائق
│   └── AI-INTEGRATION-README.md   # دليل الاستخدام الشامل
│
├── 📂 logs/                       # ملفات السجلات
│   └── (سيتم إنشاؤها تلقائياً)
│
└── 📂 tests/                      # اختبارات النظام
    └── (سيتم إضافتها لاحقاً)
```

## 🚀 البدء السريع

### 1. تشغيل النظام من VS Code
```
Ctrl + Shift + P → Tasks: Run Task → اختر المهمة المطلوبة
```

### 2. تشغيل النظام من PowerShell
```powershell
cd ai-integration-system
.\quick-start.ps1
```

### 3. فحص حالة النظام
```bash
python scripts/ai_integration_controller.py --status
```

## 🔧 الملفات الرئيسية

### ملفات التكوين
- **`config/ai-integration-system.json`** - إعدادات النظام الشاملة
- **`config/.env.integration`** - متغيرات البيئة والمسارات

### السكريبتات
- **`scripts/ai_integration_controller.py`** - منسق Python للتحكم الكامل
- **`scripts/ai-integration-manager.ps1`** - واجهة PowerShell التفاعلية

### الوثائق
- **`docs/AI-INTEGRATION-README.md`** - دليل الاستخدام الكامل والتفصيلي

## 🎯 الاستخدامات الشائعة

### فحص النظام
```bash
# فحص سريع
python scripts/ai_integration_controller.py --status

# تقرير مفصل
python scripts/ai_integration_controller.py --report
```

### استعلام Gemini
```bash
# من Python
python scripts/ai_integration_controller.py --query "سؤالك هنا"

# مباشرة (من C:/Users/<USER>
gemini "سؤالك هنا"
```

### تشغيل الوكلاء
```bash
# وكيل محدد
python scripts/ai_integration_controller.py --agent memory

# من PowerShell
powershell ../ai-agents/run-agents.ps1 memory
```

## 🔗 الروابط المهمة

### الخدمات المحلية
- **AnythingLLM**: http://localhost:4001
- **n8n**: http://localhost:5678  
- **Ollama**: http://localhost:11434

### المجلدات ذات الصلة
- **AI Agents**: `../ai-agents/`
- **VS Code Settings**: `../.vscode/`
- **Memory**: `../memory/`

## 📊 حالة النظام الحالية

### المكونات
- ✅ **Gemini CLI**: متاح في `C:/Users/<USER>/gemini-cli`
- ✅ **AI Agents**: متاحة في `../ai-agents/`
- ⚠️ **AnythingLLM**: يعمل لكن يحتاج إعداد embedding
- ✅ **n8n**: يعمل بشكل طبيعي
- ✅ **Ollama**: متاح مع عدة نماذج

### المشاكل المعروفة
1. **AnythingLLM**: `No embedding base path was set`
2. **استهلاك الموارد**: عالي مع تشغيل عدة نماذج
3. **المنافذ**: غير موحدة (4001, 5678, 11434)

## 🛠️ الصيانة

### تنظيف السجلات
```bash
# حذف السجلات القديمة (أكثر من 30 يوم)
python scripts/ai_integration_controller.py --cleanup
```

### تحديث التكوين
```bash
# تحرير التكوين الرئيسي
code config/ai-integration-system.json

# تحرير متغيرات البيئة
code config/.env.integration
```

### النسخ الاحتياطي
```bash
# إنشاء نسخة احتياطية
python scripts/ai_integration_controller.py --backup
```

## 🔄 التحديثات

### إضافة مكونات جديدة
1. تحديث `config/ai-integration-system.json`
2. إضافة المسارات في `config/.env.integration`
3. تحديث VS Code tasks في `../.vscode/tasks.json`

### إضافة وكلاء جدد
1. إنشاء الوكيل في `../ai-agents/`
2. تحديث `scripts/ai_integration_controller.py`
3. إضافة في قائمة `scripts/ai-integration-manager.ps1`

## 📞 الدعم

### الأوامر المساعدة
```bash
# مساعدة Python
python scripts/ai_integration_controller.py --help

# مساعدة PowerShell
.\scripts\ai-integration-manager.ps1 -Help
```

### الموارد
- **التكوين**: `config/`
- **السجلات**: `logs/`
- **الوثائق**: `docs/`
- **الاختبارات**: `tests/`

---

**📅 تم الإنشاء**: 2025-01-06  
**👤 المطور**: Amr Ashour  
**🔄 آخر تحديث**: 2025-01-06  
**📝 الإصدار**: 1.0.0
