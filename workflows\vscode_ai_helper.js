#!/usr/bin/env node

/**
 * 🤖 VS Code AI Helper
 * ====================
 * مساعد ذكي للتفاعل مع نظام AI المتكامل من داخل VS Code
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

// إعدادات النظام
const CONFIG = {
    AI_COORDINATOR_URL: 'http://localhost:4003',
    OLLAMA_URL: 'http://localhost:11434',
    N8N_URL: 'http://localhost:4002',
    ANYTHINGLLM_URL: 'http://localhost:4001'
};

// الألوان للـ console
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// دالة لإرسال طلب HTTP
async function makeRequest(url, method = 'GET', data = null) {
    const https = require('https');
    const http = require('http');
    const urlModule = require('url');
    
    return new Promise((resolve, reject) => {
        const parsedUrl = urlModule.parse(url);
        const client = parsedUrl.protocol === 'https:' ? https : http;
        
        const options = {
            hostname: parsedUrl.hostname,
            port: parsedUrl.port,
            path: parsedUrl.path,
            method: method,
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const req = client.request(options, (res) => {
            let responseData = '';
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(responseData);
                    resolve(jsonData);
                } catch (e) {
                    resolve(responseData);
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }
        req.end();
    });
}

// دالة لقراءة الملف المحدد في VS Code
function getCurrentFileContent() {
    // هذا مثال - في الواقع ستحتاج لتمرير مسار الملف كمعامل
    const currentFile = process.argv[2];
    if (currentFile && fs.existsSync(currentFile)) {
        return fs.readFileSync(currentFile, 'utf8');
    }
    return null;
}

// دالة لتحليل الكود باستخدام AI
async function analyzeCode(code, analysisType = 'review') {
    log('🔍 تحليل الكود باستخدام AI...', 'yellow');
    
    const prompt = `قم بتحليل هذا الكود وقدم اقتراحات للتحسين:

\`\`\`
${code}
\`\`\`

نوع التحليل: ${analysisType}`;

    try {
        const response = await makeRequest(`${CONFIG.AI_COORDINATOR_URL}/api/coordinate`, 'POST', {
            prompt: prompt,
            options: {
                priority: 'normal',
                complexity: 'medium'
            }
        });

        log('✅ تم تحليل الكود بنجاح:', 'green');
        log(response.response || response, 'cyan');
        
        return response;
    } catch (error) {
        log(`❌ خطأ في تحليل الكود: ${error.message}`, 'red');
        return null;
    }
}

// دالة لتوليد كود جديد
async function generateCode(description, language = 'javascript') {
    log('🚀 توليد كود جديد...', 'yellow');
    
    const prompt = `اكتب كود ${language} لتنفيذ المهمة التالية:

${description}

يرجى كتابة كود نظيف ومع التعليقات باللغة العربية.`;

    try {
        const response = await makeRequest(`${CONFIG.AI_COORDINATOR_URL}/api/coordinate`, 'POST', {
            prompt: prompt,
            options: {
                priority: 'normal',
                complexity: 'high'
            }
        });

        log('✅ تم توليد الكود بنجاح:', 'green');
        log(response.response || response, 'cyan');
        
        return response;
    } catch (error) {
        log(`❌ خطأ في توليد الكود: ${error.message}`, 'red');
        return null;
    }
}

// دالة لفحص حالة النظام
async function checkSystemHealth() {
    log('🏥 فحص حالة النظام...', 'yellow');
    
    const services = [
        { name: 'AI Coordinator', url: `${CONFIG.AI_COORDINATOR_URL}/api/health` },
        { name: 'Ollama', url: `${CONFIG.OLLAMA_URL}/api/tags` }
    ];

    for (const service of services) {
        try {
            await makeRequest(service.url);
            log(`✅ ${service.name}: يعمل بنجاح`, 'green');
        } catch (error) {
            log(`❌ ${service.name}: لا يعمل`, 'red');
        }
    }
}

// دالة لعرض المساعدة
function showHelp() {
    log('🤖 VS Code AI Helper - دليل الاستخدام', 'bright');
    log('=====================================', 'bright');
    log('');
    log('الأوامر المتاحة:', 'yellow');
    log('  analyze <file>     - تحليل ملف الكود', 'cyan');
    log('  generate <desc>    - توليد كود جديد', 'cyan');
    log('  health            - فحص حالة النظام', 'cyan');
    log('  help              - عرض هذه المساعدة', 'cyan');
    log('');
    log('أمثلة:', 'yellow');
    log('  node vscode_ai_helper.js analyze myfile.js', 'magenta');
    log('  node vscode_ai_helper.js generate "دالة لحساب المتوسط"', 'magenta');
    log('  node vscode_ai_helper.js health', 'magenta');
}

// الدالة الرئيسية
async function main() {
    const command = process.argv[2];
    const argument = process.argv[3];

    log('🤖 VS Code AI Helper', 'bright');
    log('====================', 'bright');

    switch (command) {
        case 'analyze':
            if (argument) {
                const code = fs.readFileSync(argument, 'utf8');
                await analyzeCode(code);
            } else {
                log('❌ يرجى تحديد مسار الملف', 'red');
            }
            break;

        case 'generate':
            if (argument) {
                await generateCode(argument);
            } else {
                log('❌ يرجى تحديد وصف الكود المطلوب', 'red');
            }
            break;

        case 'health':
            await checkSystemHealth();
            break;

        case 'help':
        default:
            showHelp();
            break;
    }
}

// تشغيل البرنامج
if (require.main === module) {
    main().catch(error => {
        log(`❌ خطأ: ${error.message}`, 'red');
        process.exit(1);
    });
}

module.exports = {
    analyzeCode,
    generateCode,
    checkSystemHealth
};
