// 🧪 ملف اختبار للـ AI Analysis
// هذا ملف JavaScript بسيط لاختبار تحليل الكود

function calculateSum(a, b) {
    return a + b;
}

function calculateAverage(numbers) {
    if (numbers.length === 0) {
        return 0;
    }
    
    let sum = 0;
    for (let i = 0; i < numbers.length; i++) {
        sum += numbers[i];
    }
    
    return sum / numbers.length;
}

// دالة بها مشكلة بسيطة (لاختبار قدرة AI على اكتشاف المشاكل)
function findMaximum(arr) {
    let max = arr[0];
    for (let i = 1; i < arr.length; i++) {
        if (arr[i] > max) {
            max = arr[i];
        }
    }
    return max; // مشكلة: لا تتعامل مع المصفوفة الفارغة
}

// استخدام الدوال
const numbers = [1, 2, 3, 4, 5];
console.log("Sum:", calculateSum(10, 20));
console.log("Average:", calculateAverage(numbers));
console.log("Maximum:", findMaximum(numbers));

// TODO: إضافة المزيد من الدوال
// TODO: تحسين معالجة الأخطاء
