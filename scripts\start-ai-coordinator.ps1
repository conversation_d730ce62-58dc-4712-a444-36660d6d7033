# AI Coordinator - Quick Start Script
# ===================================
# This script starts the AI Coordinator project specifically

Write-Host "🧠 AI Coordinator - Quick Start" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan
Write-Host ""

# Function to print colored output
function Write-Status {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param($Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if Docker is running
Write-Status "Checking Docker status..."
try {
    docker info | Out-Null
    Write-Success "Docker is running"
} catch {
    Write-Error "Docker is not running. Please start Docker Desktop first."
    exit 1
}

# Check if we're in the right directory
if (-not (Test-Path "docker-compose.yml")) {
    Write-Error "docker-compose.yml not found. Please run from project root."
    exit 1
}

# Check if AI Coordinator directory exists
if (-not (Test-Path "model_mix/ai-coordinator")) {
    Write-Error "AI Coordinator directory not found at model_mix/ai-coordinator"
    exit 1
}

# Start the unified system with focus on AI Coordinator
Write-Status "Starting AI Coordinator and dependencies..."
docker-compose up -d ai-coordinator

# Wait for services to start
Write-Status "Waiting for AI Coordinator to initialize..."
Start-Sleep -Seconds 15

# Check AI Coordinator status
Write-Status "Checking AI Coordinator status..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3333/api/health" -Method Get -TimeoutSec 10 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Success "AI Coordinator is running on port 3333"
        Write-Host "           URL: http://localhost:3333" -ForegroundColor Gray
    }
} catch {
    Write-Warning "AI Coordinator may still be starting..."
    Write-Host "           URL: http://localhost:3333" -ForegroundColor Gray
}

Write-Host ""
Write-Host "🧠 AI Coordinator API Endpoints:" -ForegroundColor Cyan
Write-Host "================================"
Write-Host "• Health Check:     GET  http://localhost:3333/api/health"
Write-Host "• Smart Coordinate: POST http://localhost:3333/api/coordinate"
Write-Host "• Model Collaborate: POST http://localhost:3333/api/collaborate"

Write-Host ""
Write-Host "📋 Quick Test Commands:" -ForegroundColor Cyan
Write-Host "======================"
Write-Host "# Test health"
Write-Host 'Invoke-WebRequest -Uri "http://localhost:3333/api/health"'
Write-Host ""
Write-Host "# Test coordination (fast task)"
Write-Host '$body = @{prompt="Hello"; options=@{priority="fast"}} | ConvertTo-Json'
Write-Host 'Invoke-WebRequest -Uri "http://localhost:3333/api/coordinate" -Method POST -Body $body -ContentType "application/json"'

Write-Host ""
Write-Host "🔗 Related Services:" -ForegroundColor Cyan
Write-Host "==================="
Write-Host "• AnythingLLM:  http://localhost:3001"
Write-Host "• Ollama WebUI: http://localhost:3000"
Write-Host "• n8n:          http://localhost:5678"
Write-Host "• Ollama API:   http://localhost:11434"

Write-Host ""
Write-Host "🛠️ Management Commands:" -ForegroundColor Cyan
Write-Host "======================"
Write-Host "• View logs:        docker-compose logs -f ai-coordinator"
Write-Host "• Restart:          docker-compose restart ai-coordinator"
Write-Host "• Stop:             docker-compose stop ai-coordinator"
Write-Host "• Full system:      .\start-unified.ps1"

Write-Host ""
Write-Success "AI Coordinator is ready!"
Write-Status "Visit http://localhost:3333/api/health to verify it's working."

# Optional: Open browser to health endpoint
# Write-Status "Opening AI Coordinator health check in browser..."
# Start-Process "http://localhost:3333/api/health"
