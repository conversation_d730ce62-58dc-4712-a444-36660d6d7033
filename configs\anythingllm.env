# AnythingLLM Configuration for AI Development Assistant
# =====================================================

# Storage Configuration
STORAGE_DIR=/app/server/storage
JWT_SECRET=ai-development-assistant-2025-jwt-secret-key-very-long-and-secure

# LLM Provider Configuration - Ollama
LLM_PROVIDER=ollama
OLLAMA_BASE_PATH=http://host.docker.internal:11434
OLLAMA_MODEL_PREF=llama3:8b
OLLAMA_MODEL_TOKEN_LIMIT=4096
OLLAMA_KEEP_ALIVE_TIMEOUT=300
OLLAMA_PERFORMANCE_MODE=base

# Embedding Configuration - Using Ollama
EMBEDDING_ENGINE=ollama
EMBEDDING_BASE_PATH=http://host.docker.internal:11434
EMBEDDING_MODEL_PREF=llama3:8b
EMBEDDING_MODEL_MAX_CHUNK_LENGTH=8192

# Vector Database Configuration
VECTOR_DB=lancedb

# Audio Configuration
WHISPER_PROVIDER=local
TTS_PROVIDER=native

# Security Configuration
PASSWORDMINCHAR=8
DISABLE_TELEMETRY=true

# UI Configuration
ENABLE_DARK_MODE=true
DEFAULT_WORKSPACE=AI-Development-Assistant

# Performance Configuration
MAX_CONCURRENT_REQUESTS=5
REQUEST_TIMEOUT=30000

# Logging Configuration
LOG_LEVEL=info
ENABLE_DEBUG_LOGS=false

# Integration Configuration
ENABLE_API=true
API_RATE_LIMIT=100

# Gemini API Integration (for advanced features)
GEMINI_API_KEY=AIzaSyCbwKjebsrFkpK3tWxa6OgjNwOWcr5mpF4

# Development Mode
NODE_ENV=production
DEBUG=false
