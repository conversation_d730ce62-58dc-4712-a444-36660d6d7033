# 🧪 اختبار نظام AI المتكامل
# ================================

Write-Host "🚀 بدء اختبار نظام AI المتكامل..." -ForegroundColor Green

# اختبار 1: فحص حالة الخدمات
Write-Host "`n📊 فحص حالة الخدمات..." -ForegroundColor Yellow

$services = @(
    @{Name="AI Coordinator"; URL="http://localhost:4003/api/health"},
    @{Name="AnythingLLM"; URL="http://localhost:4001/api/v1/system/health"},
    @{Name="n8n"; URL="http://localhost:4002/healthz"},
    @{Name="Ollama"; URL="http://localhost:11434/api/tags"}
)

foreach ($service in $services) {
    try {
        $response = Invoke-WebRequest -Uri $service.URL -Method GET -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $($service.Name): يعمل بنجاح" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "❌ $($service.Name): لا يعمل" -ForegroundColor Red
    }
}

# اختبار 2: اختبار AI Coordinator
Write-Host "`n🤖 اختبار AI Coordinator..." -ForegroundColor Yellow

$testPrompt = @{
    prompt = "مرحبا، اختبار سريع للنظام"
    options = @{
        priority = "fast"
        complexity = "low"
    }
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "http://localhost:4003/api/coordinate" -Method POST -ContentType "application/json" -Body $testPrompt
    Write-Host "✅ AI Coordinator يعمل بنجاح" -ForegroundColor Green
    Write-Host "📝 الرد: $($response.response)" -ForegroundColor Cyan
}
catch {
    Write-Host "❌ خطأ في AI Coordinator: $($_.Exception.Message)" -ForegroundColor Red
}

# اختبار 3: اختبار Ollama مباشرة
Write-Host "`n🦙 اختبار Ollama..." -ForegroundColor Yellow

$ollamaTest = @{
    model = "llama3:8b"
    prompt = "قل مرحبا بالعربية"
    stream = $false
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "http://localhost:11434/api/generate" -Method POST -ContentType "application/json" -Body $ollamaTest
    Write-Host "✅ Ollama يعمل بنجاح" -ForegroundColor Green
    Write-Host "📝 الرد: $($response.response)" -ForegroundColor Cyan
}
catch {
    Write-Host "❌ خطأ في Ollama: $($_.Exception.Message)" -ForegroundColor Red
}

# اختبار 4: عرض النماذج المتاحة
Write-Host "`n📋 النماذج المتاحة في Ollama..." -ForegroundColor Yellow

try {
    $models = ollama list
    Write-Host $models -ForegroundColor Cyan
}
catch {
    Write-Host "❌ لا يمكن الوصول لقائمة النماذج" -ForegroundColor Red
}

Write-Host "`n🎉 انتهى الاختبار!" -ForegroundColor Green
Write-Host "📖 راجع QUICK_START_GUIDE.md للخطوات التالية" -ForegroundColor Blue
