# 🔧 إصلاح مشاكل الخدمات - AI Development Assistant
# ========================================================

Write-Host "🚀 بدء إصلاح مشاكل الخدمات..." -ForegroundColor Green

# التحقق من Docker Desktop
Write-Host "🔍 فحص حالة Docker Desktop..." -ForegroundColor Yellow
$dockerProcess = Get-Process "Docker Desktop" -ErrorAction SilentlyContinue

if (-not $dockerProcess) {
    Write-Host "❌ Docker Desktop غير مشغل. يرجى تشغيل Docker Desktop أولاً." -ForegroundColor Red
    Write-Host "📝 خطوات الحل:" -ForegroundColor Cyan
    Write-Host "   1. افتح Docker Desktop من قائمة Start" -ForegroundColor White
    Write-Host "   2. انتظر حتى يكتمل التحميل" -ForegroundColor White
    Write-Host "   3. تأكد من ظهور أيقونة Docker في شريط المهام" -ForegroundColor White
    Write-Host "   4. أعد تشغيل هذا السكريبت" -ForegroundColor White
    
    # محاولة تشغيل Docker Desktop تلقائياً
    Write-Host "🔄 محاولة تشغيل Docker Desktop..." -ForegroundColor Yellow
    try {
        Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe" -ErrorAction Stop
        Write-Host "✅ تم تشغيل Docker Desktop. انتظر 30 ثانية..." -ForegroundColor Green
        Start-Sleep -Seconds 30
    }
    catch {
        Write-Host "❌ فشل في تشغيل Docker Desktop تلقائياً. يرجى تشغيله يدوياً." -ForegroundColor Red
        exit 1
    }
}
else {
    Write-Host "✅ Docker Desktop يعمل بالفعل." -ForegroundColor Green
}

# التحقق من اتصال Docker
Write-Host "🔍 فحص اتصال Docker..." -ForegroundColor Yellow
try {
    docker version | Out-Null
    Write-Host "✅ Docker يعمل بشكل صحيح." -ForegroundColor Green
}
catch {
    Write-Host "❌ مشكلة في اتصال Docker. يرجى إعادة تشغيل Docker Desktop." -ForegroundColor Red
    exit 1
}

# إنشاء ملف .env إذا لم يكن موجوداً
Write-Host "🔍 فحص ملف .env..." -ForegroundColor Yellow
if (-not (Test-Path ".env")) {
    Write-Host "📝 إنشاء ملف .env..." -ForegroundColor Yellow
    Copy-Item "configs/anythingllm.env" ".env"
    Write-Host "✅ تم إنشاء ملف .env." -ForegroundColor Green
}

# إضافة المتغيرات المفقودة
Write-Host "🔧 إضافة المتغيرات المفقودة..." -ForegroundColor Yellow
$envContent = Get-Content ".env" -Raw

$missingVars = @{
    "AUTH_TOKEN" = "ai-dev-assistant-auth-token-2025"
    "SIG_KEY" = "ai-dev-assistant-sig-key-2025"
    "SIG_SALT" = "ai-dev-assistant-sig-salt-2025"
    "N8N_ENCRYPTION_KEY" = "ai-dev-assistant-n8n-encryption-2025"
    "WEBUI_SECRET_KEY" = "ai-dev-assistant-webui-secret-2025"
    "N8N_BASIC_AUTH_USER" = "admin"
    "N8N_BASIC_AUTH_PASSWORD" = "admin123"
    "GOOGLE_API_KEY" = "AIzaSyCbwKjebsrFkpK3tWxa6OgjNwOWcr5mpF4"
}

foreach ($var in $missingVars.GetEnumerator()) {
    if ($envContent -notmatch "$($var.Key)=") {
        Add-Content ".env" "`n$($var.Key)=$($var.Value)"
        Write-Host "   ✅ أضيف: $($var.Key)" -ForegroundColor Green
    }
}

# إيقاف الخدمات الموجودة
Write-Host "🛑 إيقاف الخدمات الموجودة..." -ForegroundColor Yellow
docker-compose down --remove-orphans 2>$null

# بناء الصور المطلوبة
Write-Host "🏗️ بناء صور Docker..." -ForegroundColor Yellow
if (Test-Path "model_mix/ai-coordinator/Dockerfile") {
    docker-compose build ai-coordinator
    Write-Host "✅ تم بناء صورة ai-coordinator." -ForegroundColor Green
}

# بدء الخدمات
Write-Host "🚀 بدء الخدمات..." -ForegroundColor Yellow
docker-compose up -d

# انتظار بدء الخدمات
Write-Host "⏳ انتظار بدء الخدمات (60 ثانية)..." -ForegroundColor Yellow
Start-Sleep -Seconds 60

# فحص حالة الخدمات
Write-Host "🔍 فحص حالة الخدمات..." -ForegroundColor Yellow
docker-compose ps

Write-Host "`n🎉 تم الانتهاء من إصلاح الخدمات!" -ForegroundColor Green
Write-Host "📋 الخدمات المتاحة:" -ForegroundColor Cyan
Write-Host "   • AnythingLLM: http://localhost:4001" -ForegroundColor White
Write-Host "   • n8n: http://localhost:4002" -ForegroundColor White
Write-Host "   • AI Coordinator: http://localhost:4003" -ForegroundColor White

Write-Host "`n🔧 إذا واجهت مشاكل:" -ForegroundColor Yellow
Write-Host "   1. تأكد من تشغيل Docker Desktop" -ForegroundColor White
Write-Host "   2. تأكد من عدم استخدام المنافذ من برامج أخرى" -ForegroundColor White
Write-Host "   3. أعد تشغيل السكريبت" -ForegroundColor White
