# AI Integration Manager
# مدير نظام التكامل الذكي

param(
    [Parameter(Position=0)]
    [string]$Action = "menu",
    
    [Parameter(Position=1)]
    [string]$Target = "",
    
    [switch]$Verbose,
    [switch]$Force
)

# تحميل إعدادات البيئة
if (Test-Path ".env.integration") {
    Get-Content ".env.integration" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
        }
    }
}

function Write-ColoredOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Show-Banner {
    Write-ColoredOutput "🤖 AI Integration Manager" "Cyan"
    Write-ColoredOutput "=========================" "Cyan"
    Write-ColoredOutput "نظام إدارة التكامل الذكي" "Yellow"
    Write-ColoredOutput ""
}

function Test-ServiceHealth {
    param([string]$Url, [string]$Name)
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-ColoredOutput "✅ $Name: متصل" "Green"
            return $true
        }
    }
    catch {
        Write-ColoredOutput "❌ $Name: غير متاح" "Red"
        return $false
    }
}

function Get-SystemStatus {
    Write-ColoredOutput "`n📊 فحص حالة النظام..." "Yellow"
    Write-ColoredOutput "===================" "Yellow"
    
    # فحص الخدمات
    $services = @{
        "Ollama" = "http://localhost:11434/api/tags"
        "AnythingLLM" = "http://localhost:4001"
        "n8n" = "http://localhost:5678"
    }
    
    $healthyServices = 0
    foreach ($service in $services.GetEnumerator()) {
        if (Test-ServiceHealth -Url $service.Value -Name $service.Key) {
            $healthyServices++
        }
    }
    
    # فحص Gemini CLI
    try {
        $geminiResult = & gemini --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-ColoredOutput "✅ Gemini CLI: متاح" "Green"
            $healthyServices++
        }
    }
    catch {
        Write-ColoredOutput "❌ Gemini CLI: غير متاح" "Red"
    }
    
    # فحص الوكلاء
    $agentsPath = "ai-agents"
    if (Test-Path $agentsPath) {
        $agentFiles = Get-ChildItem "$agentsPath\*-agent.py"
        Write-ColoredOutput "✅ الوكلاء: $($agentFiles.Count) وكيل متاح" "Green"
    } else {
        Write-ColoredOutput "❌ الوكلاء: مجلد غير موجود" "Red"
    }
    
    # تقييم الحالة العامة
    $totalServices = 4
    $healthPercentage = ($healthyServices / $totalServices) * 100
    
    Write-ColoredOutput "`n📈 الحالة العامة: $healthPercentage%" $(if ($healthPercentage -gt 75) { "Green" } elseif ($healthPercentage -gt 50) { "Yellow" } else { "Red" })
}

function Start-GeminiQuery {
    param([string]$Query)
    
    if (-not $Query) {
        $Query = Read-Host "🤖 أدخل استعلامك لـ Gemini"
    }
    
    Write-ColoredOutput "`n🚀 تشغيل استعلام Gemini..." "Yellow"
    Write-ColoredOutput "الاستعلام: $Query" "Cyan"
    Write-ColoredOutput "=" * 50 "Gray"
    
    try {
        # تغيير المجلد إلى المجلد الجذر
        Push-Location "C:\Users\<USER>\$AgentName-agent.py"
    if (-not (Test-Path $agentFile)) {
        Write-ColoredOutput "❌ الوكيل $AgentName غير موجود" "Red"
        return
    }
    
    Write-ColoredOutput "`n🤖 تشغيل الوكيل: $AgentName" "Yellow"
    Write-ColoredOutput "=" * 30 "Gray"
    
    try {
        python $agentFile
        Write-ColoredOutput "✅ تم تشغيل الوكيل بنجاح" "Green"
    }
    catch {
        Write-ColoredOutput "❌ فشل في تشغيل الوكيل: $($_.Exception.Message)" "Red"
    }
}

function Get-OllamaModels {
    Write-ColoredOutput "`n🧠 النماذج المتاحة في Ollama:" "Yellow"
    Write-ColoredOutput "=========================" "Yellow"
    
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:11434/api/tags" -TimeoutSec 10
        if ($response.models) {
            foreach ($model in $response.models) {
                $size = [math]::Round($model.size / 1GB, 2)
                Write-ColoredOutput "• $($model.name) ($size GB)" "Cyan"
            }
        } else {
            Write-ColoredOutput "❌ لا توجد نماذج متاحة" "Red"
        }
    }
    catch {
        Write-ColoredOutput "❌ فشل في الاتصال بـ Ollama: $($_.Exception.Message)" "Red"
    }
}

function Start-IntegrationTest {
    Write-ColoredOutput "`n🧪 اختبار التكامل الشامل..." "Yellow"
    Write-ColoredOutput "=========================" "Yellow"
    
    # اختبار Python Controller
    Write-ColoredOutput "`n1️⃣ اختبار Python Controller..." "Cyan"
    try {
        python ai_integration_controller.py --status
    }
    catch {
        Write-ColoredOutput "❌ فشل اختبار Python Controller" "Red"
    }
    
    # اختبار Gemini CLI
    Write-ColoredOutput "`n2️⃣ اختبار Gemini CLI..." "Cyan"
    Start-GeminiQuery "مرحبا، هل تعمل بشكل صحيح؟"
    
    # اختبار الوكلاء
    Write-ColoredOutput "`n3️⃣ اختبار الوكلاء..." "Cyan"
    if (Test-Path "ai-agents\run-agents.ps1") {
        & "ai-agents\run-agents.ps1" test
    }
    
    Write-ColoredOutput "`n✅ انتهى اختبار التكامل" "Green"
}

function Open-Services {
    Write-ColoredOutput "`n🌐 فتح واجهات الخدمات..." "Yellow"
    
    $services = @{
        "AnythingLLM" = "http://localhost:4001"
        "n8n" = "http://localhost:5678"
    }
    
    foreach ($service in $services.GetEnumerator()) {
        Write-ColoredOutput "🔗 فتح $($service.Key)..." "Cyan"
        Start-Process $service.Value
        Start-Sleep 1
    }
}

function Show-Menu {
    Show-Banner
    
    Write-ColoredOutput "📋 الخيارات المتاحة:" "Yellow"
    Write-ColoredOutput "=================" "Yellow"
    Write-ColoredOutput "1. 📊 فحص حالة النظام" "White"
    Write-ColoredOutput "2. 🤖 استعلام Gemini CLI" "White"
    Write-ColoredOutput "3. 🤖 تشغيل وكيل" "White"
    Write-ColoredOutput "4. 🧠 عرض نماذج Ollama" "White"
    Write-ColoredOutput "5. 🧪 اختبار التكامل" "White"
    Write-ColoredOutput "6. 🌐 فتح واجهات الخدمات" "White"
    Write-ColoredOutput "7. 📝 إنشاء تقرير شامل" "White"
    Write-ColoredOutput "8. ⚙️ إعدادات النظام" "White"
    Write-ColoredOutput "0. 🚪 خروج" "White"
    
    do {
        $choice = Read-Host "`n🎯 اختر رقم الخيار"
        
        switch ($choice) {
            "1" { Get-SystemStatus }
            "2" { Start-GeminiQuery }
            "3" { Start-Agent }
            "4" { Get-OllamaModels }
            "5" { Start-IntegrationTest }
            "6" { Open-Services }
            "7" { python ai_integration_controller.py --report }
            "8" { code ai-integration-system.json }
            "0" { 
                Write-ColoredOutput "`n👋 شكراً لاستخدام نظام التكامل الذكي!" "Green"
                exit 
            }
            default { Write-ColoredOutput "❌ اختيار غير صحيح" "Red" }
        }
        
        if ($choice -ne "0") {
            Read-Host "`n⏸️ اضغط Enter للمتابعة"
        }
        
    } while ($choice -ne "0")
}

# تشغيل الإجراء المطلوب
switch ($Action.ToLower()) {
    "status" { Get-SystemStatus }
    "query" { Start-GeminiQuery -Query $Target }
    "agent" { Start-Agent -AgentName $Target }
    "models" { Get-OllamaModels }
    "test" { Start-IntegrationTest }
    "open" { Open-Services }
    "report" { python ai_integration_controller.py --report }
    default { Show-Menu }
}
