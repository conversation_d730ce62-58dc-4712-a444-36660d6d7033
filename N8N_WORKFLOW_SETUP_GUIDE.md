# 🔧 دليل إعداد n8n Workflow المتكامل
## Complete n8n Workflow Setup Guide

---

## 📋 الخطوة 1: الدخول إلى n8n

### معلومات الدخول:
- **الرابط**: http://localhost:4002
- **المستخدم**: `admin`
- **كلمة المرور**: `password123`

---

## 📥 الخطوة 2: استيراد Workflow الجديد

### 2.1 استيراد الملف:
1. افتح http://localhost:4002
2. انقر على **"Add workflow"** أو الزر **"+"**
3. انقر على الثلاث نقاط **"⋯"** في الأعلى
4. اختر **"Import from file"**
5. اختر الملف: `ai_integrated_workflow.json`
6. انقر **"Import"**

### 2.2 الـ Workflows المتاحة:
- ✅ `ai_integrated_workflow.json` - **الجديد والمحدث**
- ✅ `model_mix/ai-coordinator/n8n-workflow.json` - الأساسي
- ✅ `core/workflows/ai_dev_assistant_workflow.json` - القديم

---

## 🔐 الخطوة 3: إعداد Credentials

### 3.1 إعداد Gemini API Key:
1. اذهب إلى **Settings** → **Credentials**
2. انقر على **"Add Credential"**
3. اختر **"HTTP Header Auth"**
4. أدخل:
   - **Name**: `Gemini API Key`
   - **Header Name**: `x-goog-api-key`
   - **Header Value**: `AIzaSyCbwKjebsrFkpK3tWxa6OgjNwOWcr5mpF4`
5. احفظ الـ credential

### 3.2 تحديث عقد Workflow:
1. انقر على عقدة **"Gemini Strategic Analysis"**
2. في قسم **Credentials**، اختر `Gemini API Key`
3. احفظ التغييرات

---

## ⚙️ الخطوة 4: تكوين العقد

### 4.1 عقدة Webhook Start:
- **Path**: `ai-dev-assistant`
- **Method**: POST
- **Response Mode**: Response Node

### 4.2 عقدة Complexity Decision:
- **Condition**: `{{$json.body.complexity}}` equals `high`
- **True**: يذهب لـ Gemini Strategic Analysis
- **False**: يذهب لـ AI Coordinator

### 4.3 عقدة Gemini Strategic Analysis:
- **URL**: `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent`
- **Method**: POST
- **Headers**: Content-Type: application/json
- **Authentication**: HTTP Header Auth (Gemini API Key)

### 4.4 عقدة AI Coordinator:
- **URL**: `http://ai-coordinator:3333/api/coordinate`
- **Method**: POST
- **Headers**: Content-Type: application/json

### 4.5 عقدة Ollama Execution:
- **URL**: `http://host.docker.internal:11434/api/generate`
- **Method**: POST
- **Model**: `llama3:8b`
- **Stream**: false

### 4.6 عقدة AnythingLLM Memory:
- **URL**: `http://anythingllm:3001/api/v1/workspace/ai-development/chat`
- **Method**: POST
- **Headers**: 
  - Content-Type: application/json
  - Authorization: Bearer {{$env.AUTH_TOKEN}}

---

## 🎯 الخطوة 5: تفعيل Workflow

1. بعد إكمال الإعداد، انقر على **"Activate"** في الأعلى
2. ستحصل على webhook URL مثل:
   ```
   http://localhost:4002/webhook/ai-dev-assistant
   ```

---

## 🧪 الخطوة 6: اختبار Workflow

### 6.1 اختبار بسيط (complexity: medium):
```powershell
$body = @{
    prompt = "اكتب دالة JavaScript لحساب المتوسط"
    complexity = "medium"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:4002/webhook/ai-dev-assistant" -Method POST -ContentType "application/json" -Body $body
```

### 6.2 اختبار معقد (complexity: high):
```powershell
$body = @{
    prompt = "صمم نظام إدارة مشاريع كامل مع قاعدة بيانات"
    complexity = "high"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:4002/webhook/ai-dev-assistant" -Method POST -ContentType "application/json" -Body $body
```

---

## 🔄 سير العمل المتوقع

### للمهام البسيطة (complexity: medium):
```
Webhook → Complexity Decision → AI Coordinator → AnythingLLM Memory → Response
```

### للمهام المعقدة (complexity: high):
```
Webhook → Complexity Decision → Gemini Strategic Analysis → Ollama Execution → AnythingLLM Memory → Response
```

---

## 📊 مراقبة التنفيذ

### عرض Executions:
1. انقر على **"Executions"** في القائمة الجانبية
2. ستجد قائمة بجميع التشغيلات
3. انقر على أي تشغيل لرؤية التفاصيل

### تتبع الأخطاء:
- التشغيلات الفاشلة تظهر باللون الأحمر
- انقر عليها لرؤية رسالة الخطأ
- تحقق من logs كل عقدة

---

## ❌ استكشاف الأخطاء

### مشكلة: Gemini API لا يعمل
- تحقق من صحة API Key
- تأكد من أن الـ credential مربوط بالعقدة
- تحقق من format الطلب

### مشكلة: AI Coordinator لا يستجيب
- تأكد من أن AI Coordinator يعمل: `docker ps | grep ai-coordinator`
- تحقق من URL: `http://ai-coordinator:3333`

### مشكلة: Ollama لا يعمل
- تحقق من أن Ollama يعمل محلياً: `ollama list`
- تأكد من URL: `http://host.docker.internal:11434`

### مشكلة: AnythingLLM لا يستجيب
- تحقق من أن AnythingLLM يعمل: `docker ps | grep anythingllm`
- تأكد من وجود workspace بالاسم `ai-development`

---

## 🎉 النجاح!

إذا تمكنت من:
- ✅ استيراد workflow بنجاح
- ✅ إعداد جميع credentials
- ✅ تفعيل workflow
- ✅ الحصول على استجابة من الاختبار

فأنت جاهز للخطوة التالية!

---

**💡 نصيحة**: احفظ نسخة احتياطية من workflow بعد الإعداد الناجح.
