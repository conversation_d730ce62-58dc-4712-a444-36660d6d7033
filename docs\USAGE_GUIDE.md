# 🎯 دليل الاستخدام - النهج المختلط
## Usage Guide: AnythingLLM + VS Code Integration

---

## 🏠 **AnythingLLM - الواجهة الرئيسية للمحادثات**

### متى تستخدم AnythingLLM:
- 💬 **المحادثات الطويلة والمعقدة**
- 📚 **البحث في مستندات المشروع**
- 🧠 **بناء قاعدة معرفة**
- 📋 **التخطيط الاستراتيجي**
- 🔍 **تحليل المتطلبات**

### كيفية الوصول:
- **الرابط**: http://localhost:4001
- **الإعداد**: مربوط بـ Ollama محلياً + Gemini API

### الميزات المتاحة:
- ✅ **4 نماذج محلية**: llama3:8b, gemma3n:e4b, mistral:7b, phi3:mini
- ✅ **Gemini Pro API**: للمهام المعقدة
- ✅ **Workspaces منفصلة**: لكل مشروع
- ✅ **رفع المستندات**: PDF, Word, Text
- ✅ **ذاكرة طويلة المدى**: تذكر المحادثات السابقة

---

## 💻 **VS Code - للمهام البرمجية السريعة**

### متى تستخدم VS Code:
- ⚡ **مساعدة سريعة أثناء البرمجة**
- 🔍 **تحليل الكود المفتوح**
- 📝 **إنشاء ملفات جديدة**
- 🐛 **إصلاح الأخطاء**
- 🧪 **اختبار أفكار سريعة**

### كيفية الاستخدام:
1. اضغط `Ctrl+Shift+P`
2. اكتب "Tasks: Run Task"
3. اختر المهمة المطلوبة:
   - 🤖 **AI: تحليل الملف الحالي**
   - 🚀 **AI: توليد كود جديد**
   - 🏥 **AI: فحص حالة النظام**

### الأوامر المباشرة:
```bash
# تحليل ملف
node vscode_ai_helper.js analyze myfile.js

# توليد كود
node vscode_ai_helper.js generate "دالة لحساب المتوسط"

# فحص النظام
node vscode_ai_helper.js health
```

---

## 🔄 **سير العمل المقترح**

### للمشاريع الجديدة:
```
1. AnythingLLM → تخطيط المشروع وتحليل المتطلبات
2. VS Code → كتابة الكود والتطوير
3. AnythingLLM → مراجعة وتحسين الكود
4. VS Code → التطبيق النهائي
```

### للمشاكل المعقدة:
```
1. AnythingLLM → فهم المشكلة وتحليلها
2. AnythingLLM → البحث في المستندات ذات الصلة
3. VS Code → تطبيق الحل
4. AnythingLLM → توثيق الحل
```

### للمهام السريعة:
```
VS Code → مباشرة للحل السريع
```

---

## 🎯 **أمثلة عملية**

### مثال 1: تطوير ميزة جديدة

#### الخطوة 1 - التخطيط في AnythingLLM:
```
المستخدم: أريد إنشاء نظام تسجيل دخول للموقع
AnythingLLM: سأحلل المتطلبات وأضع خطة مفصلة...
```

#### الخطوة 2 - التطوير في VS Code:
```
Ctrl+Shift+P → "AI: توليد كود جديد"
الوصف: "نموذج تسجيل دخول HTML مع validation"
```

#### الخطوة 3 - المراجعة في AnythingLLM:
```
المستخدم: [رفع الكود المُنشأ]
AnythingLLM: إليك اقتراحات للتحسين...
```

### مثال 2: حل مشكلة برمجية

#### في VS Code مباشرة:
```
# فتح الملف المُشكِل
Ctrl+Shift+P → "AI: تحليل الملف الحالي"
# الحصول على تشخيص فوري
```

### مثال 3: بحث في المستندات

#### في AnythingLLM:
```
المستخدم: كيف أستخدم React Hooks في مشروعي؟
AnythingLLM: [يبحث في مستندات React المرفوعة]
```

---

## ⚙️ **الإعدادات المطلوبة**

### AnythingLLM Setup:
1. افتح http://localhost:4001
2. أكمل الإعداد الأولي
3. اختر **Ollama** كـ LLM Provider
4. استخدم العنوان: `http://host.docker.internal:11434`
5. اختر النموذج: `llama3:8b`
6. أنشئ workspace للمشروع

### VS Code Setup:
- ✅ **المهام جاهزة**: في `.vscode/tasks.json`
- ✅ **AI Helper جاهز**: `vscode_ai_helper.js`
- ✅ **Scripts جاهزة**: للاختبار والتحليل

---

## 🎛️ **التحكم في النماذج**

### اختيار النموذج المناسب:

#### للمهام السريعة:
- **phi3:mini** (2.2 GB) - الأسرع
- **mistral:7b** (4.1 GB) - متوازن

#### للمهام المعقدة:
- **llama3:8b** (4.7 GB) - الأفضل عموماً
- **gemma3n:e4b** (7.5 GB) - الأقوى محلياً
- **Gemini Pro API** - للمهام الاستراتيجية

### تبديل النماذج:
- **في AnythingLLM**: من إعدادات الـ workspace
- **في VS Code**: تحديث `vscode_ai_helper.js`

---

## 📊 **مراقبة الاستخدام**

### فحص حالة النظام:
```bash
# من VS Code
Ctrl+Shift+P → "AI: فحص حالة النظام"

# من Terminal
powershell -ExecutionPolicy Bypass -File test_complete_integration.ps1
```

### الروابط المهمة:
- **AnythingLLM**: http://localhost:4001
- **n8n**: http://localhost:4002 (للـ workflows المتقدمة)
- **AI Coordinator**: http://localhost:4003 (للتنسيق)

---

## 🎉 **الخلاصة**

### الاستخدام اليومي:
- 🏠 **AnythingLLM**: للتفكير والتخطيط والبحث
- 💻 **VS Code**: للتطوير والكتابة السريعة
- 🔄 **التنقل بينهما**: حسب طبيعة المهمة

### النتيجة:
- ⚡ **سرعة في المهام البسيطة** (VS Code)
- 🧠 **عمق في المهام المعقدة** (AnythingLLM)
- 🔗 **تكامل سلس** بين الاثنين

**🎯 هذا النهج يحقق أفضل استفادة من قوة كل أداة!**
